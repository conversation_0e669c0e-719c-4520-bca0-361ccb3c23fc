#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 快速调试启动脚本
直接启动API + Worker调试系统
"""
import os
import sys
import subprocess
from pathlib import Path


def main():
    """主函数"""
    print("🚀 AI小说生成器 - 快速调试启动")
    print("=" * 40)
    
    # 检查是否在项目根目录
    if not os.path.exists("app"):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查统一启动脚本是否存在
    debug_script = "scripts/start_debug_system.py"
    if not os.path.exists(debug_script):
        print(f"❌ 错误：{debug_script} 不存在")
        sys.exit(1)
    
    print("正在启动统一调试系统...")
    print("包含：FastAPI服务 + Celery Worker")
    print("=" * 40)
    
    try:
        # 直接调用统一启动脚本
        subprocess.run([sys.executable, debug_script], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统已停止")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败，返回码: {e.returncode}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ 启动时发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 