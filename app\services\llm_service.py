#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LLM服务 - 集成多模型支持
基于旧版本llm_adapters的成熟逻辑
"""
import asyncio
import logging
import time
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod
import openai

from app.core.exceptions import LLMServiceException, ErrorCode
from app.core.logging_config import log_error

logger = logging.getLogger(__name__)


class LLMAdapter(ABC):
    """LLM适配器抽象基类"""
    
    def __init__(self, api_key: str, base_url: str, model_name: str, 
                 temperature: float = 0.7, max_tokens: int = 2048, timeout: int = 600):
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.timeout = timeout
    
    @abstractmethod
    async def invoke(self, prompt: str) -> str:
        """调用模型生成内容"""
        pass
    
    @abstractmethod
    async def stream_invoke(self, prompt: str, callback=None):
        """流式调用模型生成内容"""
        pass


class OpenAIAdapter(LLMAdapter):
    """OpenAI适配器"""
    
    async def invoke(self, prompt: str) -> str:
        """调用OpenAI API"""
        try:
            import openai
            
            client = openai.AsyncClient(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout
            )
            
            response = await client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            return response.choices[0].message.content or ""
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    async def stream_invoke(self, prompt: str, callback=None):
        """流式调用OpenAI API"""
        try:
            
            client = openai.AsyncClient(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout
            )
            
            stream = await client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True
            )
            
            full_content = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    full_content += content
                    if callback:
                        await callback(content, full_content)
            
            return full_content
            
        except Exception as e:
            logger.error(f"OpenAI stream API call failed: {e}")
            raise


class AzureAdapter(LLMAdapter):
    """Azure OpenAI适配器"""
    
    async def invoke(self, prompt: str) -> str:
        """调用Azure OpenAI API"""
        try:
            import openai
            
            client = openai.AsyncClient(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout
            )
            
            response = await client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            return response.choices[0].message.content or ""
            
        except Exception as e:
            logger.error(f"Azure OpenAI API call failed: {e}")
            raise
    
    async def stream_invoke(self, prompt: str, callback=None):
        """流式调用Azure OpenAI API"""
        try:
            import openai
            
            client = openai.AsyncClient(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout
            )
            
            stream = await client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True
            )
            
            full_content = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    full_content += content
                    if callback:
                        await callback(content, full_content)
            
            return full_content
            
        except Exception as e:
            logger.error(f"Azure stream API call failed: {e}")
            raise


class DashscopeAdapter(LLMAdapter):
    """阿里云通义千问适配器"""
    
    async def invoke(self, prompt: str) -> str:
        """调用通义千问API"""
        try:
            import dashscope
            
            dashscope.api_key = self.api_key
            
            response = await asyncio.to_thread(
                dashscope.Generation.call,
                model=self.model_name,
                prompt=prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            if response.status_code == 200:
                return response.output.text
            else:
                raise Exception(f"Dashscope API error: {response.message}")
            
        except Exception as e:
            logger.error(f"Dashscope API call failed: {e}")
            raise
    
    async def stream_invoke(self, prompt: str, callback=None):
        """流式调用通义千问API"""
        try:
            import dashscope
            
            dashscope.api_key = self.api_key
            
            # Dashscope流式调用
            responses = await asyncio.to_thread(
                dashscope.Generation.call,
                model=self.model_name,
                prompt=prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                stream=True
            )
            
            full_content = ""
            for response in responses:
                if response.status_code == 200:
                    content = response.output.text
                    full_content += content
                    if callback:
                        await callback(content, full_content)
                else:
                    raise Exception(f"Dashscope stream API error: {response.message}")
            
            return full_content
            
        except Exception as e:
            logger.error(f"Dashscope stream API call failed: {e}")
            # 如果流式调用失败，回退到普通调用
            return await self.invoke(prompt)


class LLMService:
    """LLM服务管理器"""
    
    def __init__(self):
        self.adapters: Dict[str, LLMAdapter] = {}
        self.default_adapter: Optional[str] = None
    
    def register_adapter(self, name: str, adapter: LLMAdapter):
        """注册LLM适配器"""
        self.adapters[name] = adapter
        if self.default_adapter is None:
            self.default_adapter = name
    
    def set_default_adapter(self, name: str):
        """设置默认适配器"""
        if name in self.adapters:
            self.default_adapter = name
        else:
            raise ValueError(f"Adapter {name} not found")
    
    async def invoke_with_retry(self, prompt: str, adapter_name: Optional[str] = None, 
                               max_retries: int = 3, request_id: Optional[str] = None) -> str:
        """带重试机制的调用，继承自旧版本common.py的重试逻辑"""
        from app.core.exceptions import LLMServiceException, ErrorCode
        from app.core.logging_config import log_error
        
        adapter_name = adapter_name or self.default_adapter
        if not adapter_name or adapter_name not in self.adapters:
            raise LLMServiceException(
                message="没有可用的LLM适配器",
                error_code=ErrorCode.LLM_CONFIG_INVALID
            )
        
        adapter = self.adapters[adapter_name]
        
        for attempt in range(1, max_retries + 1):
            try:
                # 添加请求前的清洗，移除think标签等
                cleaned_prompt = self._clean_prompt(prompt)
                
                result = await adapter.invoke(cleaned_prompt)
                
                # 清洗响应内容
                cleaned_result = self._clean_response(result)
                
                logger.info(f"LLM调用成功 - 适配器: {adapter_name}, 尝试次数: {attempt}", extra={
                    'request_id': request_id or 'unknown',
                    'operation': 'llm_invoke'
                })
                return cleaned_result
                
            except Exception as e:
                logger.warning(f"LLM调用失败 - 适配器: {adapter_name}, 尝试次数: {attempt}, 错误: {str(e)}", extra={
                    'request_id': request_id or 'unknown',
                    'operation': 'llm_invoke'
                })
                
                if attempt < max_retries:
                    # 指数退避
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)
                else:
                    log_error(
                        logger=logger,
                        error=e,
                        context={
                            "adapter_name": adapter_name,
                            "max_retries": max_retries,
                            "prompt_length": len(prompt)
                        },
                        request_id=request_id
                    )
                    raise LLMServiceException(
                        message=f"LLM服务调用失败，已重试{max_retries}次",
                        error_code=ErrorCode.LLM_API_ERROR
                    )
        
        return ""
    
    async def stream_invoke_with_retry(self, prompt: str, callback=None, adapter_name: Optional[str] = None, 
                                     max_retries: int = 3, request_id: Optional[str] = None) -> str:
        """带重试机制的流式调用"""
        
        adapter_name = adapter_name or self.default_adapter
        if not adapter_name or adapter_name not in self.adapters:
            raise LLMServiceException(
                message="没有可用的LLM适配器",
                error_code=ErrorCode.LLM_CONFIG_INVALID
            )
        
        adapter = self.adapters[adapter_name]
        
        for attempt in range(1, max_retries + 1):
            try:
                # 添加请求前的清洗
                cleaned_prompt = self._clean_prompt(prompt)
                
                result = await adapter.stream_invoke(cleaned_prompt, callback)
                
                # 清洗响应内容
                cleaned_result = self._clean_response(result)
                
                logger.info(f"LLM流式调用成功 - 适配器: {adapter_name}, 尝试次数: {attempt}", extra={
                    'request_id': request_id or 'unknown',
                    'operation': 'llm_stream_invoke'
                })
                return cleaned_result
                
            except Exception as e:
                logger.warning(f"LLM流式调用失败 - 适配器: {adapter_name}, 尝试次数: {attempt}, 错误: {str(e)}", extra={
                    'request_id': request_id or 'unknown',
                    'operation': 'llm_stream_invoke'
                })
                
                if attempt < max_retries:
                    # 指数退避
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)
                else:
                    log_error(
                        logger=logger,
                        error=e,
                        context={
                            "adapter_name": adapter_name,
                            "max_retries": max_retries,
                            "prompt_length": len(prompt)
                        },
                        request_id=request_id
                    )
                    raise LLMServiceException(
                        message=f"LLM流式服务调用失败，已重试{max_retries}次",
                        error_code=ErrorCode.LLM_API_ERROR
                    )
        
        return ""
    
    def _clean_prompt(self, prompt: str) -> str:
        """清洗提示词"""
        # 移除可能的格式问题
        return prompt.strip()
    
    def _clean_response(self, response: str) -> str:
        """清洗响应内容，基于旧版本common.py的清洗逻辑"""
        import re
        
        if not response:
            return ""
        
        # 移除<think>标签包裹的内容
        cleaned = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)
        
        # 移除多余空白
        cleaned = cleaned.strip()
        
        return cleaned


# 全局LLM服务实例
llm_service = LLMService()


def create_llm_adapter(interface_format: str, api_key: str, base_url: str, 
                      model_name: str, temperature: float = 0.7, 
                      max_tokens: int = 2048, timeout: int = 600) -> LLMAdapter:
    """创建LLM适配器工厂函数，兼容旧版本接口"""
    
    if interface_format.lower() == "openai":
        return OpenAIAdapter(api_key, base_url, model_name, temperature, max_tokens, timeout)
    elif interface_format.lower() == "azure":
        return AzureAdapter(api_key, base_url, model_name, temperature, max_tokens, timeout)
    elif interface_format.lower() == "dashscope":
        return DashscopeAdapter(api_key, base_url, model_name, temperature, max_tokens, timeout)
    else:
        raise ValueError(f"Unsupported interface format: {interface_format}")


async def initialize_default_llm_service():
    """初始化默认LLM服务"""
    try:
        from app.core.config import settings
        
        # 检查必要的配置
        if not settings.LLM_API_KEY:
            logger.warning("LLM_API_KEY not configured, LLM service will not be available")
            return False
        
        # 验证和设置默认值
        provider = getattr(settings, 'LLM_PROVIDER', 'openai').lower()
        base_url = getattr(settings, 'LLM_BASE_URL', 'https://api.openai.com/v1')
        model_name = getattr(settings, 'LLM_MODEL', 'gpt-3.5-turbo')
        temperature = float(getattr(settings, 'LLM_TEMPERATURE', 0.7))
        max_tokens = int(getattr(settings, 'LLM_MAX_TOKENS', 2048))
        timeout = int(getattr(settings, 'LLM_TIMEOUT', 600))
        
        # 确保参数范围有效
        temperature = max(0.0, min(2.0, temperature))  # 限制在[0.0, 2.0]范围内
        max_tokens = max(1, min(8192, max_tokens))      # 限制在[1, 8192]范围内
        timeout = max(30, min(1800, timeout))          # 限制在[30, 1800]范围内
        
        logger.info(f"Initializing LLM service with provider: {provider}, model: {model_name}")
        
        # 根据配置决定使用哪个适配器
        if provider == "openai":
            adapter = create_llm_adapter(
                interface_format="openai",
                api_key=settings.LLM_API_KEY,
                base_url=base_url,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )
        elif provider == "azure":
            azure_endpoint = getattr(settings, 'AZURE_OPENAI_ENDPOINT', None)
            if not azure_endpoint:
                logger.error("Azure OpenAI endpoint not configured")
                return False
            adapter = create_llm_adapter(
                interface_format="azure",
                api_key=settings.LLM_API_KEY,
                base_url=azure_endpoint,
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )
        elif provider == "dashscope":
            # 对于Dashscope，优先使用专门的API key
            api_key = getattr(settings, 'DASHSCOPE_API_KEY', None) or settings.LLM_API_KEY
            if not api_key:
                logger.error("Dashscope API key not configured")
                return False
            adapter = create_llm_adapter(
                interface_format="dashscope",
                api_key=api_key,
                base_url="",  # Dashscope不需要base_url
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=timeout
            )
        else:
            logger.error(f"Unsupported LLM provider: {provider}")
            return False
        
        llm_service.register_adapter("default", adapter)
        logger.info(f"LLM service initialized successfully with provider: {provider}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize default LLM service: {e}")
        return False 