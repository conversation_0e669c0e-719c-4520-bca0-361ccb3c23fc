#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI生成相关API端点
"""
import uuid
import json
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    NotFoundException, QuotaExceededException, LLMServiceException, 
    DatabaseException, ErrorCode
)
from app.models.user import User
from app.models.novel import Novel
from app.models.chapter import Chapter, ChapterStatus
from app.models.generation_task import GenerationTask, TaskType, TaskStatus
from app.schemas.generation import (
    ArchitectureGenerationRequest, ArchitectureGenerationResponse,
    ChapterGenerationRequest, ChapterGenerationResponse,
    CharacterGenerationRequest, CharacterGenerationResponse
)
from app.services.auth_service import AuthService
# 导入实际的任务函数
from app.services.generation_tasks import generate_novel_architecture, generate_chapter_content
from uuid import UUID

logger = get_logger(__name__)

router = APIRouter()


@router.post("/architecture", response_model=ArchitectureGenerationResponse, summary="生成小说架构")
async def generate_architecture(
    request_data: ArchitectureGenerationRequest,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    生成小说整体架构
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证用户配额
        if current_user.quota_used >= current_user.quota_limit:
            raise QuotaExceededException(
                message="已达到生成配额限制",
                error_code=ErrorCode.USER_QUOTA_EXCEEDED
            )
        
        # 验证小说权限（如果提供了novel_id）
        novel = None
        if request_data.novel_id:
            novel_query = select(Novel).where(
                and_(Novel.id == request_data.novel_id, Novel.user_id == current_user.id)
            )
            novel_result = await session.execute(novel_query)
            novel = novel_result.scalar_one_or_none()
            
            if not novel:
                raise NotFoundException(
                    message="小说不存在或无权限访问",
                    error_code=ErrorCode.NOVEL_NOT_FOUND
                )
        else:
            # 如果没有提供novel_id，创建新小说
            novel = Novel(
                user_id=current_user.id,
                title=f"AI生成小说 - {request_data.theme}",
                description=request_data.theme,
                genre=request_data.genre,
                target_length=request_data.target_length,
                style_settings=request_data.style_preferences,
                status="generating"
            )
            session.add(novel)
            await session.commit()
            await session.refresh(novel)
            
            # 更新用户配额
            current_user.quota_used += 1
            await session.commit()
        
        # 调用实际的Celery任务，先获取Celery任务ID
        task = generate_novel_architecture.delay(
            task_id="",  # 占位符，稍后会更新
            user_id=str(current_user.id),  # 转换为字符串确保兼容性
            novel_id=str(novel.id),        # 转换为字符串确保兼容性
            parameters={
                "theme": request_data.theme,
                "genre": request_data.genre,
                "target_length": request_data.target_length,
                "style_preferences": request_data.style_preferences or {}
            }
        )
        
        # 使用Celery任务ID作为统一标识
        celery_task_id = task.id
        
        # 记录生成架构请求
        log_business_operation(
            logger=logger.logger,
            operation="generate_architecture",
            details={
                "novel_id": novel.id,
                "theme": request_data.theme,
                "genre": request_data.genre,
                "target_length": request_data.target_length,
                "celery_task_id": celery_task_id
            },
            user_id=user_id,
            request_id=request_id
        )
        
        # 创建数据库任务记录
        db_task = GenerationTask(
            task_id=celery_task_id,  # 使用Celery任务ID
            user_id=current_user.id,
            novel_id=novel.id,
            task_type=TaskType.GENERATE_ARCHITECTURE,
            status=TaskStatus.PENDING,
            parameters=json.dumps({
                "theme": request_data.theme,
                "genre": request_data.genre,
                "target_length": request_data.target_length,
                "style_preferences": request_data.style_preferences or {}
            })
        )
        session.add(db_task)
        await session.commit()
        await session.refresh(db_task)
        
        return ArchitectureGenerationResponse(
            task_id=str(db_task.id),  # 返回数据库主键UUID
            status="pending",
            message="架构生成任务已启动"
        )
        
    except (NotFoundException, QuotaExceededException):
        raise
    except Exception as e:
        logger.error(f"生成架构失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'generate_architecture',
            'error': str(e)
        })
        await session.rollback()
        raise LLMServiceException(
            message="生成架构失败",
            error_code=ErrorCode.GENERATION_TASK_CREATE_FAILED
        )


@router.post("/chapter", response_model=ChapterGenerationResponse, summary="生成章节内容")
async def generate_chapter(
    request_data: ChapterGenerationRequest,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    为指定小说生成章节内容
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证小说权限
        novel_query = select(Novel).where(
            and_(Novel.id == request_data.novel_id, Novel.user_id == current_user.id)
        )
        novel_result = await session.execute(novel_query)
        novel = novel_result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 验证用户配额
        if current_user.quota_used >= current_user.quota_limit:
            raise QuotaExceededException(
                message="已达到生成配额限制",
                error_code=ErrorCode.USER_QUOTA_EXCEEDED
            )
        
        # 检查章节是否已存在
        existing_chapter_query = select(Chapter).where(
            and_(
                Chapter.novel_id == request_data.novel_id,
                Chapter.chapter_number == request_data.chapter_number,
                Chapter.is_active == True
            )
        )
        existing_chapter_result = await session.execute(existing_chapter_query)
        existing_chapter = existing_chapter_result.scalar_one_or_none()
        
        if existing_chapter:
            chapter_id = existing_chapter.id
        else:
            # 创建新章节
            new_chapter = Chapter(
                novel_id=request_data.novel_id,
                chapter_number=request_data.chapter_number,
                title=f"第{request_data.chapter_number}章",
                status=ChapterStatus.DRAFT
            )
            session.add(new_chapter)
            await session.commit()
            await session.refresh(new_chapter)
            chapter_id = new_chapter.id
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 记录生成章节请求
        log_business_operation(
            logger=logger.logger,
            operation="generate_chapter",
            details={
                "novel_id": request_data.novel_id,
                "chapter_id": chapter_id,
                "chapter_number": request_data.chapter_number,
                "chapter_outline": request_data.chapter_outline,
                "context_chapters": request_data.context_chapters,
                "task_id": task_id
            },
            user_id=user_id,
            request_id=request_id
        )
        
        # 调用实际的Celery任务
        task = generate_chapter_content.delay(
            task_id=task_id,
            user_id=str(current_user.id),
            novel_id=str(request_data.novel_id),
            chapter_id=str(chapter_id),
            parameters={
                "chapter_number": request_data.chapter_number,
                "chapter_outline": request_data.chapter_outline,
                "context_chapters": request_data.context_chapters or [],
                "style_preferences": request_data.style_preferences or {}
            }
        )
        
        # 创建数据库任务记录
        db_task = GenerationTask(
            task_id=task.id,  # Celery任务ID
            user_id=current_user.id,
            novel_id=request_data.novel_id,
            task_type=TaskType.GENERATE_CHAPTER,
            status=TaskStatus.PENDING,
            parameters=json.dumps({
                "chapter_number": request_data.chapter_number,
                "chapter_outline": request_data.chapter_outline,
                "context_chapters": request_data.context_chapters or [],
                "style_preferences": request_data.style_preferences or {}
            }),
            result_chapter_id=chapter_id
        )
        session.add(db_task)
        await session.commit()
        await session.refresh(db_task)
        
        return ChapterGenerationResponse(
            task_id=str(db_task.id),  # 返回数据库主键UUID
            status="pending",
            message="章节生成任务已启动"
        )
        
    except (NotFoundException, QuotaExceededException):
        raise
    except Exception as e:
        logger.error(f"生成章节失败 - 用户ID: {user_id}, 小说ID: {request_data.novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'generate_chapter',
            'error': str(e)
        })
        await session.rollback()
        raise LLMServiceException(
            message="生成章节失败",
            error_code=ErrorCode.GENERATION_TASK_CREATE_FAILED
        )


@router.post("/character", response_model=CharacterGenerationResponse, summary="生成角色设定")
async def generate_character(
    request_data: CharacterGenerationRequest,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    为指定小说生成角色设定
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证小说权限
        novel_query = select(Novel).where(
            and_(Novel.id == request_data.novel_id, Novel.user_id == current_user.id)
        )
        novel_result = await session.execute(novel_query)
        novel = novel_result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 记录生成角色请求
        log_business_operation(
            logger=logger.logger,
            operation="generate_character",
            details={
                "novel_id": request_data.novel_id,
                "character_role": request_data.character_role,
                "character_traits": request_data.character_traits,
                "story_context": request_data.story_context
            },
            user_id=user_id,
            request_id=request_id
        )
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # TODO: 实现角色生成任务
        # task = generate_character_task.delay(...)
        
        return CharacterGenerationResponse(
            task_id=task_id,
            status="pending",
            message="角色生成任务已启动"
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"生成角色失败 - 用户ID: {user_id}, 小说ID: {request_data.novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'generate_character',
            'error': str(e)
        })
        raise LLMServiceException(
            message="生成角色失败",
            error_code=ErrorCode.GENERATION_TASK_CREATE_FAILED
        )


@router.post("/optimize-chapter", response_model=ChapterGenerationResponse, summary="优化章节内容")
async def optimize_chapter(
    chapter_id: UUID,
    optimization_type: str,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    优化现有章节内容
    
    optimization_type可以是: polish, expand, compress, rewrite
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证章节权限
        chapter_query = select(Chapter).join(Novel).where(
            and_(
                Chapter.id == chapter_id,
                Novel.user_id == current_user.id
            )
        )
        
        chapter_result = await session.execute(chapter_query)
        chapter = chapter_result.scalar_one_or_none()
        
        if not chapter:
            raise NotFoundException(
                message="章节不存在或无权限访问",
                error_code=ErrorCode.CHAPTER_NOT_FOUND
            )
        
        # 记录优化章节请求
        log_business_operation(
            logger=logger.logger,
            operation="optimize_chapter",
            details={
                "chapter_id": chapter_id,
                "optimization_type": optimization_type,
                "novel_id": chapter.novel_id
            },
            user_id=user_id,
            request_id=request_id
        )
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # TODO: 实现章节优化任务
        # task = optimize_chapter_task.delay(...)
        
        return ChapterGenerationResponse(
            task_id=task_id,
            status="pending",
            message=f"章节{optimization_type}优化任务已启动"
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"优化章节失败 - 用户ID: {user_id}, 章节ID: {chapter_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'optimize_chapter',
            'error': str(e)
        })
        raise LLMServiceException(
            message="优化章节失败",
            error_code=ErrorCode.GENERATION_TASK_CREATE_FAILED
        ) 
