#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebSocket连接管理
"""
import json
import logging
from typing import Dict, List
from fastapi import WebSocket, WebSocketDisconnect, APIRouter
from uuid import UUID

logger = logging.getLogger(__name__)

websocket_router = APIRouter()


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接：{task_id: [websocket1, websocket2, ...]}
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # 存储连接对应的任务ID：{websocket: task_id}
        self.connection_tasks: Dict[WebSocket, str] = {}
    
    async def connect(self, websocket: WebSocket, task_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        
        if task_id not in self.active_connections:
            self.active_connections[task_id] = []
        
        self.active_connections[task_id].append(websocket)
        self.connection_tasks[websocket] = task_id
        
        logger.info(f"WebSocket connected for task {task_id}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.connection_tasks:
            task_id = self.connection_tasks[websocket]
            
            # 从任务连接列表中移除
            if task_id in self.active_connections:
                self.active_connections[task_id].remove(websocket)
                
                # 如果该任务没有其他连接，删除任务条目
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
            
            # 从连接任务映射中移除
            del self.connection_tasks[websocket]
            
            logger.info(f"WebSocket disconnected for task {task_id}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")
    
    async def broadcast_to_task(self, task_id: str, message: dict):
        """向特定任务的所有连接广播消息"""
        if task_id in self.active_connections:
            # 复制连接列表，避免在迭代过程中修改
            connections = self.active_connections[task_id][:]
            
            for connection in connections:
                try:
                    await connection.send_text(json.dumps(message, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"Failed to send broadcast message to task {task_id}: {e}")
                    # 如果发送失败，断开连接
                    self.disconnect(connection)
    
    def get_task_connections_count(self, task_id: str) -> int:
        """获取特定任务的连接数量"""
        return len(self.active_connections.get(task_id, []))


# 全局连接管理器实例
manager = ConnectionManager()


@websocket_router.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket端点 - 用于接收任务进度更新"""
    await manager.connect(websocket, task_id)
    
    try:
        while True:
            # 保持连接活跃，接收客户端的心跳包
            data = await websocket.receive_text()
            
            # 可以处理客户端发送的消息，如心跳包、取消任务等
            try:
                message = json.loads(data)
                message_type = message.get("type")
                
                if message_type == "ping":
                    # 响应心跳包
                    await manager.send_personal_message({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }, websocket)
                elif message_type == "cancel_task":
                    # 处理取消任务请求
                    await handle_cancel_task(task_id, websocket)
                
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {data}")
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info(f"WebSocket disconnected for task {task_id}")
    except Exception as e:
        logger.error(f"WebSocket error for task {task_id}: {e}")
        manager.disconnect(websocket)


async def handle_cancel_task(task_id: str, websocket: WebSocket):
    """处理取消任务请求"""
    # 这里可以实现取消任务的逻辑
    # 例如：向Celery发送撤销任务的命令
    
    await manager.send_personal_message({
        "type": "task_cancel_received",
        "task_id": task_id,
        "message": "任务取消请求已接收"
    }, websocket)
    
    logger.info(f"Cancel request received for task {task_id}")


async def send_task_update(task_id: str, status: str, progress: int = 0, message: str = "", 
                         data: dict = None, current_stage: str = None, detailed_progress: dict = None):
    """发送任务更新消息到WebSocket客户端"""
    from datetime import datetime
    
    update_message = {
        "type": "task_update",
        "task_id": task_id,
        "status": status,
        "progress": progress,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z",
    }
    
    # 添加详细的阶段信息
    if current_stage:
        update_message["current_stage"] = current_stage
    
    # 添加详细进度信息
    if detailed_progress:
        update_message["detailed_progress"] = detailed_progress
    
    if data:
        update_message["data"] = data
    
    await manager.broadcast_to_task(task_id, update_message)
    stage_info = f" - {current_stage}" if current_stage else ""
    logger.info(f"Task update sent for {task_id}: {status} ({progress}%){stage_info}")


# 导出给其他模块使用的函数
__all__ = ["websocket_router", "manager", "send_task_update"]