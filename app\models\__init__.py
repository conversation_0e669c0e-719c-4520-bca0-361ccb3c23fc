# 导入所有模型，确保它们被注册到Base.metadata中
from app.models.user import User
from app.models.novel import Novel
from app.models.document import Document
from app.models.chapter import Chapter
from app.models.generation_task import GenerationTask
from app.models.prompt_template import PromptTemplate
from app.models.generation_state import GenerationState
from app.models.llm_config import LLMConfig, LLMUsageLog

__all__ = [
    "User",
    "Novel", 
    "Document",
    "Chapter",
    "GenerationTask",
    "PromptTemplate",
    "GenerationState",
    "LLMConfig",
    "LLMUsageLog",
] 