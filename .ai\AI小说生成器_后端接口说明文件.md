# AI小说生成器 - 后端接口说明文件

## 📋 接口概览

### 基础信息
- **API版本**: v1.0
- **基础URL**: `http://your-domain.com/api/v1`
- **协议**: HTTP/HTTPS + WebSocket
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT <PERSON>er <PERSON> + 微信小程序登录

---

## 🔐 认证机制

### JWT Bearer Token
```http
Authorization: Bearer <jwt_token>
```

### 微信小程序登录
使用微信小程序的code换取token，无需预先注册。

---

## 📝 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 实际数据内容
  },
  "message": "操作成功",
  "timestamp": "2025-01-01T00:00:00Z",
  "request_id": "abc12345"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [/* 数据数组 */],
    "total": 100,
    "page": 1,
    "per_page": 20
  },
  "message": "获取成功",
  "timestamp": "2025-01-01T00:00:00Z",
  "request_id": "abc12345"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详细信息
    }
  },
  "timestamp": "2025-01-01T00:00:00Z",
  "request_id": "abc12345",
  "path": "/api/v1/novels/550e8400-e29b-41d4-a716-************"
}
```

---

## 🚨 错误处理

### HTTP状态码
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权/认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证失败
- `429` - 请求过于频繁
- `500` - 服务器内部错误

### 业务错误码

| 错误代码 | HTTP状态码 | 说明 |
|---------|------------|------|
| GENERAL_001 | 500 | 系统内部错误 |
| GENERAL_002 | 422 | 数据验证失败 |
| GENERAL_003 | 500 | 数据库操作失败 |
| GENERAL_004 | 403 | 权限不足 |
| GENERAL_005 | 404 | 资源不存在 |
| GENERAL_006 | 429 | 配额超限 |
| AUTH_001 | 401 | 令牌无效 |
| AUTH_002 | 401 | 令牌已过期 |
| AUTH_003 | 400 | 微信登录失败 |
| AUTH_004 | 404 | 用户不存在 |
| AUTH_005 | 403 | 权限不足 |
| USER_001 | 404 | 用户不存在 |
| USER_002 | 429 | 用户配额超限 |
| USER_003 | 400 | 用户更新失败 |
| NOVEL_001 | 404 | 小说不存在 |
| NOVEL_002 | 400 | 小说创建失败 |
| NOVEL_003 | 400 | 小说更新失败 |
| NOVEL_004 | 400 | 小说删除失败 |
| NOVEL_005 | 403 | 小说访问被拒绝 |
| CHAPTER_001 | 404 | 章节不存在 |
| CHAPTER_002 | 400 | 章节创建失败 |
| CHAPTER_003 | 400 | 章节更新失败 |
| CHAPTER_004 | 400 | 章节删除失败 |
| CHAPTER_005 | 403 | 章节访问被拒绝 |
| TASK_001 | 400 | 生成任务创建失败 |
| TASK_002 | 404 | 生成任务不存在 |
| TASK_003 | 400 | 生成任务已在运行 |
| TASK_004 | 400 | 生成任务失败 |
| TASK_005 | 503 | 生成LLM错误 |
| TASK_006 | 429 | 生成配额超限 |
| LLM_001 | 404 | LLM配置不存在 |
| LLM_002 | 400 | LLM配置无效 |
| LLM_003 | 503 | LLM API错误 |
| LLM_004 | 429 | LLM配额超限 |

---

## 📊 接口分类总览

| 模块 | 前缀 | 说明 | 认证要求 |
|------|------|------|----------|
| **认证** | `/auth` | 用户登录认证 | 部分需要 |
| **用户管理** | `/users` | 用户信息管理 | ✅ 必需 |
| **小说管理** | `/novels` | 小说项目管理 | ✅ 必需 |
| **章节管理** | `/chapters` | 章节内容管理 | ✅ 必需 |
| **内容生成** | `/generation` | AI生成任务 | ✅ 必需 |
| **任务管理** | `/tasks` | 任务状态监控 | ✅ 必需 |
| **配置管理** | `/llm-configs` | LLM配置管理 | ✅ 必需 |

---

## 🔗 详细接口文档

## 1. 认证模块 (`/auth`)

### 1.1 微信小程序登录
```http
POST /api/v1/auth/wechat/login
Content-Type: application/json

{
  "code": "微信登录凭证",
  "nickname": "用户昵称(可选)",
  "avatar_url": "用户头像URL(可选)"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "wx_openid": "wx_openid_123",
      "nickname": "用户昵称",
      "avatar_url": "https://avatar.url",
      "quota_used": 0,
      "quota_limit": 10,
      "is_vip": false
    }
  }
}
```

### 1.2 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <token>
```

### 1.3 获取当前用户信息
```http
GET /api/v1/auth/me
Authorization: Bearer <token>
```

### 1.4 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

---

## 2. 用户管理模块 (`/users`)

### 2.1 获取用户档案
```http
GET /api/v1/users/profile
Authorization: Bearer <token>
```

### 2.2 更新用户档案
```http
PUT /api/v1/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "nickname": "新昵称(可选)",
  "avatar_url": "新头像URL(可选)"
}
```

### 2.3 获取用户配额信息
```http
GET /api/v1/users/quota
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "quota_used": 2,
    "quota_limit": 10,
    "quota_remaining": 8,
    "is_vip": false
  }
}
```

---

## 3. 小说管理模块 (`/novels`)

### 3.1 创建新小说
```http
POST /api/v1/novels/
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "我的AI小说",
  "description": "小说简介(可选)",
  "genre": "science_fiction",
  "target_length": 50000,
  "style_settings": {
    "style": "细腻温馨",
    "pace": "medium"
  }
}
```

**支持的小说类型 (`genre`)**:
- `science_fiction` - 科幻
- `romance` - 言情
- `fantasy` - 奇幻
- `mystery` - 悬疑
- `urban` - 都市
- `historical` - 历史
- `martial_arts` - 武侠

### 3.2 获取用户小说列表
```http
GET /api/v1/novels/?skip=0&limit=20&status_filter=draft
Authorization: Bearer <token>
```

**查询参数**:
- `skip`: 跳过数量 (默认0)
- `limit`: 每页数量 (默认20，最大100)
- `status_filter`: 状态筛选 (`draft`/`generating`/`completed`/`paused`)

### 3.3 获取小说详情
```http
GET /api/v1/novels/{novel_id}
Authorization: Bearer <token>
```

### 3.4 更新小说信息
```http
PUT /api/v1/novels/{novel_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "新标题(可选)",
  "description": "新简介(可选)",
  "genre": "新类型(可选)",
  "target_length": 60000,
  "style_settings": {
    "style": "新风格"
  }
}
```

### 3.5 删除小说
```http
DELETE /api/v1/novels/{novel_id}
Authorization: Bearer <token>
```

---

## 4. 章节管理模块 (`/chapters`)

### 4.1 创建新章节
```http
POST /api/v1/chapters/
Authorization: Bearer <token>
Content-Type: application/json

{
  "novel_id": "550e8400-e29b-41d4-a716-************",
  "title": "第一章：开始",
  "content": "章节内容(可选)",
  "summary": "章节摘要(可选)"
}
```

### 4.2 获取小说章节列表
```http
GET /api/v1/chapters/novel/{novel_id}?skip=0&limit=50
Authorization: Bearer <token>
```

### 4.3 获取章节详情
```http
GET /api/v1/chapters/{chapter_id}
Authorization: Bearer <token>
```

### 4.4 更新章节
```http
PUT /api/v1/chapters/{chapter_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "新标题(可选)",
  "content": "新内容(可选)",
  "summary": "新摘要(可选)"
}
```

### 4.5 删除章节
```http
DELETE /api/v1/chapters/{chapter_id}
Authorization: Bearer <token>
```

---

## 5. 内容生成模块 (`/generation`)

### 5.1 生成架构
```http
POST /api/v1/generation/architecture
Authorization: Bearer <token>
Content-Type: application/json

{
  "novel_id": "550e8400-e29b-41d4-a716-************",
  "theme": "未来世界的爱情故事", 
  "genre": "science_fiction",
  "target_length": 50000,
  "style_preferences": {
    "style": "细腻温馨",
    "pace": "medium",
    "focus": "character_development"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-************", 
    "status": "pending",
    "message": "架构生成任务已启动"
  }
}
```

### 5.2 生成章节内容
```http
POST /api/v1/generation/chapter
Authorization: Bearer <token>
Content-Type: application/json

{
  "novel_id": "550e8400-e29b-41d4-a716-************",
  "chapter_number": 1,
  "chapter_outline": "主角遇到转折点(可选)",
  "context_chapters": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"],
  "style_preferences": {
    "style": "细腻温馨",
    "tone": "轻松"
  }
}
```

### 5.3 生成角色设定
```http
POST /api/v1/generation/character
Authorization: Bearer <token>
Content-Type: application/json

{
  "novel_id": "550e8400-e29b-41d4-a716-************",
  "character_role": "主角",
  "character_traits": ["勇敢", "善良", "聪明"],
  "story_context": "末世背景下的生存故事"
}
```

### 5.4 优化章节内容
```http
POST /api/v1/generation/optimize-chapter?chapter_id=550e8400-e29b-41d4-a716-************&optimization_type=polish
Authorization: Bearer <token>
```

**优化类型**:
- `polish` - 文字润色
- `expand` - 内容扩展  
- `compress` - 内容压缩
- `rewrite` - 重写



---

## 6. 任务管理模块 (`/tasks`)

### 6.1 获取用户任务列表
```http
GET /api/v1/tasks/?skip=0&limit=20&status_filter=running
Authorization: Bearer <token>
```

**查询参数**:
- `skip`: 跳过数量
- `limit`: 每页数量
- `status_filter`: 状态筛选 (`pending`/`running`/`completed`/`failed`/`cancelled`)

### 6.2 获取任务详情
```http
GET /api/v1/tasks/{task_id}
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "task_type": "chapter_generation",
    "status": "running",
    "progress": 75.5,
    "error_message": null,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:15:00Z"
  }
}
```

### 6.3 取消任务
```http
DELETE /api/v1/tasks/{task_id}
Authorization: Bearer <token>
```

### 6.4 获取任务进度
```http
GET /api/v1/tasks/{task_id}/progress
Authorization: Bearer <token>
```

---

## 7. LLM配置管理模块 (`/llm-configs`)

### 7.1 获取LLM配置列表
```http
GET /api/v1/llm-configs/
Authorization: Bearer <token>
```

### 7.2 创建LLM配置
```http
POST /api/v1/llm-configs/
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "OpenAI GPT-4",
  "provider": "openai",
  "model": "gpt-4",
  "api_key": "sk-...",
  "base_url": "https://api.openai.com/v1",
  "temperature": 0.7,
  "max_tokens": 2048,
  "is_active": true
}
```

### 7.3 获取LLM配置详情
```http
GET /api/v1/llm-configs/{config_id}
Authorization: Bearer <token>
```

### 7.4 更新LLM配置
```http
PUT /api/v1/llm-configs/{config_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "更新的配置名称",
  "temperature": 0.8,
  "max_tokens": 4096
}
```

### 7.5 删除LLM配置
```http
DELETE /api/v1/llm-configs/{config_id}
Authorization: Bearer <token>
```

### 7.6 获取默认配置
```http
GET /api/v1/llm-configs/default/config
Authorization: Bearer <token>
```

### 7.7 设置默认配置
```http
POST /api/v1/llm-configs/{config_id}/set-default
Authorization: Bearer <token>
```

### 7.8 测试LLM配置
```http
POST /api/v1/llm-configs/test
Authorization: Bearer <token>
Content-Type: application/json

{
  "config_id": "550e8400-e29b-41d4-a716-************",
  "test_prompt": "你好，请介绍一下你自己"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "response": "你好！我是一个AI助手...",
    "response_time": 1.23,
    "token_usage": {
      "prompt_tokens": 10,
      "completion_tokens": 25,
      "total_tokens": 35
    }
  }
}
```

### 7.9 获取配置使用统计
```http
GET /api/v1/llm-configs/{config_id}/usage-stats?days=30
Authorization: Bearer <token>
```

### 7.10 获取总体使用统计
```http
GET /api/v1/llm-configs/usage/stats?days=30
Authorization: Bearer <token>
```

### 7.11 复制配置
```http
POST /api/v1/llm-configs/{config_id}/duplicate?new_name=复制的配置
Authorization: Bearer <token>
```

### 7.12 获取可用提供商
```http
GET /api/v1/llm-configs/providers/available
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "providers": [
      {
        "name": "openai",
        "display_name": "OpenAI",
        "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
      },
      {
        "name": "azure", 
        "display_name": "Azure OpenAI",
        "models": ["gpt-3.5-turbo", "gpt-4"]
      },
      {
        "name": "dashscope",
        "display_name": "通义千问",
        "models": ["qwen-turbo", "qwen-plus", "qwen-max"]
      }
    ]
  }
}
```

### 7.13 切换配置状态
```http
POST /api/v1/llm-configs/{config_id}/toggle-status
Authorization: Bearer <token>
```

---

## 🔌 WebSocket通信

### 连接地址
```
ws://your-domain.com/ws/{task_id}
```

### 客户端消息
```json
{
  "type": "ping",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

```json
{
  "type": "cancel_task"
}
```

### 服务端推送事件

#### 任务更新通知
```json
{
  "type": "task_update",
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "IN_PROGRESS",
  "progress": 75,
  "message": "正在生成第3章...",
  "timestamp": "2025-01-01T00:00:00Z",
  "data": {
    "chapter_number": 3,
    "current_step": "content_generation"
  }
}
```

#### 心跳包响应
```json
{
  "type": "pong",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

#### 任务取消确认
```json
{
  "type": "task_cancel_received",
  "task_id": "550e8400-e29b-41d4-a716-************",
  "message": "任务取消请求已接收"
}
```

---

## 📈 接口限制和配额

### 请求频率限制
- **API限流**: 100次请求/分钟/IP
- **用户限流**: 基于JWT token识别用户
- **WebSocket**: 每用户最多5个并发连接

### 用户配额系统
```json
{
  "quota_used": 2,      // 已使用配额
  "quota_limit": 10,    // 配额上限
  "quota_remaining": 8, // 剩余配额
  "is_vip": false      // VIP状态
}
```

### 数据大小限制
- **请求体**: 最大10MB
- **章节内容**: 最大50万字符
- **文件上传**: 最大50MB

---

## 🧪 接口测试示例

### JavaScript示例
```javascript
// 登录
async function login(code) {
  const response = await fetch('/api/v1/auth/wechat/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ code })
  });
  const result = await response.json();
  return result.data.access_token;
}

// 创建小说
async function createNovel(token, novelData) {
  const response = await fetch('/api/v1/novels/', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(novelData)
  });
  return response.json();
}

// WebSocket连接
function connectWebSocket(taskId) {
  const ws = new WebSocket(`ws://localhost:8000/ws/${taskId}`);
  
  ws.onopen = () => {
    // 发送心跳包
    ws.send(JSON.stringify({
      type: 'ping',
      timestamp: new Date().toISOString()
    }));
  };
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'task_update') {
      handleTaskUpdate(data);
    } else if (data.type === 'pong') {
      console.log('Heart beat response received');
    }
  };
}
```

### Python示例
```python
import requests
import json

class NovelAPIClient:
    def __init__(self, base_url="http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.token = None
    
    def login(self, code):
        url = f"{self.base_url}/auth/wechat/login"
        response = requests.post(url, json={"code": code})
        result = response.json()
        self.token = result["data"]["access_token"]
        return result
    
    def create_novel(self, title, genre):
        url = f"{self.base_url}/novels/"
        headers = {"Authorization": f"Bearer {self.token}"}
        data = {"title": title, "genre": genre}
        response = requests.post(url, json=data, headers=headers)
        return response.json()
```

---

## 📋 完整接口列表

| 方法 | 路径 | 说明 | 认证 |
|------|------|------|------|
| **认证模块** |
| POST | `/auth/wechat/login` | 微信登录 | ❌ |
| POST | `/auth/refresh` | 刷新Token | ✅ |
| GET | `/auth/me` | 当前用户 | ✅ |
| POST | `/auth/logout` | 登出 | ✅ |
| **用户模块** |
| GET | `/users/profile` | 用户档案 | ✅ |
| PUT | `/users/profile` | 更新档案 | ✅ |
| GET | `/users/quota` | 配额信息 | ✅ |
| **小说模块** |
| POST | `/novels/` | 创建小说 | ✅ |
| GET | `/novels/` | 小说列表 | ✅ |
| GET | `/novels/{id}` | 小说详情 | ✅ |
| PUT | `/novels/{id}` | 更新小说 | ✅ |
| DELETE | `/novels/{id}` | 删除小说 | ✅ |
| **章节模块** |
| POST | `/chapters/` | 创建章节 | ✅ |
| GET | `/chapters/novel/{id}` | 章节列表 | ✅ |
| GET | `/chapters/{id}` | 章节详情 | ✅ |
| PUT | `/chapters/{id}` | 更新章节 | ✅ |
| DELETE | `/chapters/{id}` | 删除章节 | ✅ |
| **生成模块** |
| POST | `/generation/architecture` | 生成架构 | ✅ |
| POST | `/generation/chapter` | 生成章节 | ✅ |
| POST | `/generation/character` | 生成角色 | ✅ |
| POST | `/generation/optimize-chapter` | 优化章节 | ✅ |
| **任务模块** |
| GET | `/tasks/` | 任务列表 | ✅ |
| GET | `/tasks/{id}` | 任务详情 | ✅ |
| DELETE | `/tasks/{id}` | 取消任务 | ✅ |
| GET | `/tasks/{id}/progress` | 任务进度 | ✅ |
| **配置模块** |
| GET | `/llm-configs/` | 配置列表 | ✅ |
| POST | `/llm-configs/` | 创建配置 | ✅ |
| GET | `/llm-configs/{id}` | 配置详情 | ✅ |
| PUT | `/llm-configs/{id}` | 更新配置 | ✅ |
| DELETE | `/llm-configs/{id}` | 删除配置 | ✅ |
| GET | `/llm-configs/default/config` | 获取默认配置 | ✅ |
| POST | `/llm-configs/{id}/set-default` | 设为默认 | ✅ |
| POST | `/llm-configs/test` | 测试配置 | ✅ |
| GET | `/llm-configs/{id}/usage-stats` | 配置使用统计 | ✅ |
| GET | `/llm-configs/usage/stats` | 总体使用统计 | ✅ |
| POST | `/llm-configs/{id}/duplicate` | 复制配置 | ✅ |
| GET | `/llm-configs/providers/available` | 可用提供商 | ✅ |
| POST | `/llm-configs/{id}/toggle-status` | 切换状态 | ✅ |
| **系统接口** |
| GET | `/` | 根路径状态 | ❌ |
| GET | `/health` | 健康检查 | ❌ |

---

## 🔍 API文档访问

### Swagger UI
```
http://your-domain.com/docs
```

### ReDoc
```
http://your-domain.com/redoc
```

### OpenAPI规范
```
http://your-domain.com/api/v1/openapi.json
```

---

## 🔑 UUID接口规范

### 1. **ID字段格式**
- **所有ID字段**：现在使用UUID格式 (RFC 4122标准)
- **示例格式**：`550e8400-e29b-41d4-a716-************`
- **路径参数**：支持UUID格式验证
- **查询参数**：需要提供有效UUID

### 2. **路径参数示例**
```http
GET /api/v1/novels/550e8400-e29b-41d4-a716-************
PUT /api/v1/chapters/550e8400-e29b-41d4-a716-************
DELETE /api/v1/tasks/550e8400-e29b-41d4-a716-************
```

### 3. **请求体ID字段**
```json
{
  "novel_id": "550e8400-e29b-41d4-a716-************",
  "chapter_ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

### 4. **响应体ID字段**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "novel_id": "550e8400-e29b-41d4-a716-************"
  }
}
```

### 5. **错误响应更新**
```json
{
  "success": false,
  "error": {
    "code": "NOVEL_001",
    "message": "小说不存在",
    "details": {
      "novel_id": "550e8400-e29b-41d4-a716-************"
    }
  },
  "path": "/api/v1/novels/550e8400-e29b-41d4-a716-************"
}
```

### 6. **兼容性说明**
- **重大变更**：所有ID字段从Integer改为UUID
- **客户端影响**：需要更新所有ID字段的处理逻辑
- **数据库变更**：已完成破坏性迁移
- **测试工具**：Swagger UI自动支持UUID验证

---

**文档版本**: v2.0 (UUID重构版)  
**最后更新**: 2025年6月19日  
**API版本**: v2.0.0 