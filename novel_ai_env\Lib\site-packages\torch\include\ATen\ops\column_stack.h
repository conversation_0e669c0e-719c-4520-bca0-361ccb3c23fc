#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>



#include <ATen/ops/column_stack_ops.h>

namespace at {


// aten::column_stack(Tensor[] tensors) -> Tensor
inline at::Tensor column_stack(at::TensorList tensors) {
    return at::_ops::column_stack::call(tensors);
}

// aten::column_stack.out(Tensor[] tensors, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & column_stack_out(at::Tensor & out, at::TensorList tensors) {
    return at::_ops::column_stack_out::call(tensors, out);
}
// aten::column_stack.out(Tensor[] tensors, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & column_stack_outf(at::TensorList tensors, at::Tensor & out) {
    return at::_ops::column_stack_out::call(tensors, out);
}

}
